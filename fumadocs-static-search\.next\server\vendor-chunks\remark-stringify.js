"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-stringify";
exports.ids = ["vendor-chunks/remark-stringify"];
exports.modules = {

/***/ "(rsc)/./node_modules/remark-stringify/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/remark-stringify/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkStringify)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-markdown */ \"(rsc)/./node_modules/mdast-util-to-markdown/lib/index.js\");\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownOptions\n * @typedef {import('unified').Compiler<Root, string>} Compiler\n * @typedef {import('unified').Processor<undefined, undefined, undefined, Root, string>} Processor\n */\n\n/**\n * @typedef {Omit<ToMarkdownOptions, 'extensions'>} Options\n */\n\n\n\n/**\n * Add support for serializing to markdown.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction remarkStringify(options) {\n  /** @type {Processor} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n\n  self.compiler = compiler\n\n  /**\n   * @type {Compiler}\n   */\n  function compiler(tree) {\n    return (0,mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_0__.toMarkdown)(tree, {\n      ...self.data('settings'),\n      ...options,\n      // Note: this option is not in the readme.\n      // The goal is for it to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: self.data('toMarkdownExtensions') || []\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/remark-stringify/lib/index.js\n");

/***/ })

};
;