// @ts-nocheck -- skip type checking
import * as docs_1 from "../content/docs/index.mdx?collection=docs&hash=1755680986586"
import * as docs_0 from "../content/docs/getting-started.mdx?collection=docs&hash=1755680986586"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.doc<typeof _source.docs>([{ info: {"path":"getting-started.mdx","absolutePath":"C:/Users/<USER>/Desktop/fumadocs-static-search/fumadocs-static-search/content/docs/getting-started.mdx"}, data: docs_0 }, { info: {"path":"index.mdx","absolutePath":"C:/Users/<USER>/Desktop/fumadocs-static-search/fumadocs-static-search/content/docs/index.mdx"}, data: docs_1 }]);
export const meta = _runtime.meta<typeof _source.meta>([]);