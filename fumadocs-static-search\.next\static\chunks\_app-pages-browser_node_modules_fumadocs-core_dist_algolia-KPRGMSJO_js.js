"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_algolia-KPRGMSJO_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js":
/*!*************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: function() { return /* binding */ groupResults; },\n/* harmony export */   searchDocs: function() { return /* binding */ searchDocs; }\n/* harmony export */ });\n/* harmony import */ var _chunk_CNWEGOUF_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-CNWEGOUF.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(query, { indexName, onSearch, client, locale, tag }) {\n  if (query.trim().length === 0) return [];\n  const result = onSearch ? await onSearch(query, tag, locale) : await client.searchForHits({\n    requests: [\n      {\n        type: \"default\",\n        indexName,\n        query,\n        distinct: 5,\n        hitsPerPage: 10,\n        filters: tag ? `tag:${tag}` : void 0\n      }\n    ]\n  });\n  const highlighter = (0,_chunk_CNWEGOUF_js__WEBPACK_IMPORTED_MODULE_0__.createContentHighlighter)(query);\n  return groupResults(result.results[0].hits).flatMap((hit) => {\n    if (hit.type === \"page\") {\n      return {\n        ...hit,\n        contentWithHighlights: hit.contentWithHighlights ?? highlighter.highlight(hit.content)\n      };\n    }\n    return [];\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-KPRGMSJO.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContentHighlighter: function() { return /* binding */ createContentHighlighter; }\n/* harmony export */ });\n// src/search/shared.ts\nfunction escapeRegExp(input) {\n  return input.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction buildRegexFromQuery(q) {\n  const trimmed = q.trim();\n  if (trimmed.length === 0) return null;\n  const terms = Array.from(\n    new Set(\n      trimmed.split(/\\s+/).map((t) => t.trim()).filter(Boolean)\n    )\n  );\n  if (terms.length === 0) return null;\n  const escaped = terms.map(escapeRegExp).join(\"|\");\n  return new RegExp(`(${escaped})`, \"gi\");\n}\nfunction createContentHighlighter(query) {\n  const regex = typeof query === \"string\" ? buildRegexFromQuery(query) : query;\n  return {\n    highlight(content) {\n      if (!regex) return [{ type: \"text\", content }];\n      const out = [];\n      let i = 0;\n      for (const match of content.matchAll(regex)) {\n        if (i < match.index) {\n          out.push({\n            type: \"text\",\n            content: content.substring(i, match.index)\n          });\n        }\n        out.push({\n          type: \"text\",\n          content: match[0],\n          styles: {\n            highlight: true\n          }\n        });\n        i = match.index + match[0].length;\n      }\n      if (i < content.length) {\n        out.push({\n          type: \"text\",\n          content: content.substring(i)\n        });\n      }\n      return out;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js\n"));

/***/ })

}]);