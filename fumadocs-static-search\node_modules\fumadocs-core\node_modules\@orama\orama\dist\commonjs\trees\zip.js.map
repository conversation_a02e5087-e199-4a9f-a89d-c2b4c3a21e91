{"version": 3, "file": "zip.js", "sourceRoot": "", "sources": ["../../../src/trees/zip.ts"], "names": [], "mappings": ";;;AAEA,MAAa,OAAO;IAClB,WAAW;IACX,CAAC,CAAQ;IACT,aAAa;IACb,CAAC,CAAG;IACJ,YAAY;IACZ,CAAC,CAAQ;IACT,aAAa;IACb,CAAC,CAAsB;IACvB,cAAc;IACd,CAAC,CAAsB;IACvB,cAAc;IACd,CAAC,CAAsB;IAEvB,YAAY,GAAW,EAAE,KAAQ,EAAE,IAAY;QAC7C,IAAI,CAAC,CAAC,GAAG,GAAG,CAAA;QACZ,IAAI,CAAC,CAAC,GAAG,KAAK,CAAA;QACd,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;QACb,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;QACb,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;QACb,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;IACf,CAAC;CACF;AAtBD,0BAsBC;AAED,MAAa,OAAO;IAClB,IAAI,CAAsB;IAE1B;QACE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IAEO,UAAU;QAChB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IACxD,CAAC;IAED,MAAM,CAAC,GAAW,EAAE,KAAQ;QAC1B,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;QAC7D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,OAAO,CAAA;YACnB,OAAM;QACR,CAAC;QAED,IAAI,WAAW,GAAyB,IAAI,CAAC,IAAI,CAAA;QACjD,IAAI,MAAM,GAAyB,IAAI,CAAA;QAEvC,OAAO,WAAW,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,GAAG,WAAW,CAAA;YACpB,IAAI,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC;gBACxB,WAAW,GAAG,WAAW,CAAC,CAAC,CAAA;YAC7B,CAAC;iBAAM,IAAI,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC;gBAC/B,WAAW,GAAG,WAAW,CAAC,CAAC,CAAA;YAC7B,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,CAAC,GAAG,KAAK,CAAA;gBACrB,OAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC,GAAG,MAAM,CAAA;QAClB,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;gBACnB,MAAM,CAAC,CAAC,GAAG,OAAO,CAAA;YACpB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,CAAC,GAAG,OAAO,CAAA;YACpB,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;IACxB,CAAC;IAEO,QAAQ,CAAC,IAAgB;QAC/B,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;YAC3C,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,IAAgB;QACjC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAE,CAAA;QACjB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACZ,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;QACd,CAAC;QACD,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;QACV,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;QACZ,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QACf,CAAC;aAAM,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACd,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACd,CAAC;QACD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;IACZ,CAAC;IAEO,WAAW,CAAC,IAAgB;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAE,CAAA;QACjB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACZ,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;QACd,CAAC;QACD,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;QACV,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;QACZ,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAA;QACf,CAAC;aAAM,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACd,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACd,CAAC;QACD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;IACZ,CAAC;IAED,MAAM,CAAC,GAAW;QAChB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QACnC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACvB,CAAC;IAEO,UAAU,CAAC,IAAgB;QACjC,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;YACvB,CAAC;iBAAM,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACxB,CAAC;iBAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACxB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;YACvB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;YACjB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAClB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,GAAW;QACd,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QACnC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC7B,CAAC;IAED,QAAQ,CAAC,GAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,IAAI,CAAA;IACvC,CAAC;IAEO,YAAY,CAAC,GAAW;QAC9B,IAAI,WAAW,GAAyB,IAAI,CAAC,IAAI,CAAA;QAEjD,OAAO,WAAW,KAAK,IAAI,EAAE,CAAC;YAC5B,IAAI,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC1B,OAAO,WAAW,CAAA;YACpB,CAAC;YACD,WAAW,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA;QACnE,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,WAAW,CAAC,GAAW,EAAE,GAAW;QAClC,MAAM,OAAO,GAAQ,EAAE,CAAA;QACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACtB,CAAC;QACH,CAAC,CAAC,CAAA;QACF,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,WAAW,CAAC,GAAW;QACrB,MAAM,OAAO,GAAQ,EAAE,CAAA;QACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACtB,CAAC;QACH,CAAC,CAAC,CAAA;QACF,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,QAAQ,CAAC,GAAW;QAClB,MAAM,OAAO,GAAQ,EAAE,CAAA;QACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACtB,CAAC;QACH,CAAC,CAAC,CAAA;QACF,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,OAAO;QACL,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;YACpC,KAAK,EAAE,CAAA;QACT,CAAC,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IACd,CAAC;IAEO,gBAAgB,CAAC,IAA0B,EAAE,QAAoC;QACvF,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;QACvC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACd,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;IACzC,CAAC;IAED,cAAc,CAAC,EAAK,EAAE,GAAW;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAEnC,IAAI,IAAI,IAAI,IAAI;YAAE,OAAM;QAExB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YAChC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;gBACvB,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBAClB,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;SACjC,CAAA;IACH,CAAC;IAEO,UAAU,CAAC,IAA0B;QAC3C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1B,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;SAC3B,CAAA;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,CAAI,IAAS;QAC1B,MAAM,IAAI,GAAG,IAAI,OAAO,EAAK,CAAA;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,YAAY,CAAC,QAAa,EAAE,MAA4B;QAC9D,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,OAAO,IAAI,CAAA;QACb,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,OAAO,CAAI,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;QAC/D,IAAI,CAAC,CAAC,GAAG,MAAM,CAAA;QACf,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QAC5C,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AA7PD,0BA6PC"}