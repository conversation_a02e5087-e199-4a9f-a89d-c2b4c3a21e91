"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_orama-cloud-BYTAI6QU_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContentHighlighter: function() { return /* binding */ createContentHighlighter; }\n/* harmony export */ });\n// src/search/shared.ts\nfunction escapeRegExp(input) {\n  return input.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction buildRegexFromQuery(q) {\n  const trimmed = q.trim();\n  if (trimmed.length === 0) return null;\n  const terms = Array.from(\n    new Set(\n      trimmed.split(/\\s+/).map((t) => t.trim()).filter(Boolean)\n    )\n  );\n  if (terms.length === 0) return null;\n  const escaped = terms.map(escapeRegExp).join(\"|\");\n  return new RegExp(`(${escaped})`, \"gi\");\n}\nfunction createContentHighlighter(query) {\n  const regex = typeof query === \"string\" ? buildRegexFromQuery(query) : query;\n  return {\n    highlight(content) {\n      if (!regex) return [{ type: \"text\", content }];\n      const out = [];\n      let i = 0;\n      for (const match of content.matchAll(regex)) {\n        if (i < match.index) {\n          out.push({\n            type: \"text\",\n            content: content.substring(i, match.index)\n          });\n        }\n        out.push({\n          type: \"text\",\n          content: match[0],\n          styles: {\n            highlight: true\n          }\n        });\n        i = match.index + match[0].length;\n      }\n      if (i < content.length) {\n        out.push({\n          type: \"text\",\n          content: content.substring(i)\n        });\n      }\n      return out;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: function() { return /* binding */ removeUndefined; }\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstS0FPRU1DVEkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1LQU9FTUNUSS5qcz8zNjQ2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9yZW1vdmUtdW5kZWZpbmVkLnRzXG5mdW5jdGlvbiByZW1vdmVVbmRlZmluZWQodmFsdWUsIGRlZXAgPSBmYWxzZSkge1xuICBjb25zdCBvYmogPSB2YWx1ZTtcbiAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgIGlmIChvYmpba2V5XSA9PT0gdm9pZCAwKSBkZWxldGUgb2JqW2tleV07XG4gICAgaWYgKGRlZXAgJiYgdHlwZW9mIG9ialtrZXldID09PSBcIm9iamVjdFwiICYmIG9ialtrZXldICE9PSBudWxsKSB7XG4gICAgICByZW1vdmVVbmRlZmluZWQob2JqW2tleV0sIGRlZXApO1xuICAgIH0gZWxzZSBpZiAoZGVlcCAmJiBBcnJheS5pc0FycmF5KG9ialtrZXldKSkge1xuICAgICAgb2JqW2tleV0uZm9yRWFjaCgodikgPT4gcmVtb3ZlVW5kZWZpbmVkKHYsIGRlZXApKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQge1xuICByZW1vdmVVbmRlZmluZWRcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: function() { return /* binding */ searchDocs; }\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _chunk_CNWEGOUF_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-CNWEGOUF.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-CNWEGOUF.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, options) {\n  const highlighter = (0,_chunk_CNWEGOUF_js__WEBPACK_IMPORTED_MODULE_1__.createContentHighlighter)(query);\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {}, tag } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    for (const hit of result2.hits) {\n      const doc = hit.document;\n      list.push(\n        {\n          id: hit.id,\n          type: \"page\",\n          content: doc.title,\n          contentWithHighlights: highlighter.highlight(doc.title),\n          url: doc.path\n        },\n        {\n          id: \"page\" + hit.id,\n          type: \"text\",\n          content: doc.content,\n          contentWithHighlights: highlighter.highlight(doc.content),\n          url: doc.path\n        }\n      );\n    }\n    return list;\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          contentWithHighlights: highlighter.highlight(doc.title),\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        contentWithHighlights: highlighter.highlight(doc.content),\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/orama-cloud-BYTAI6QU.js\n"));

/***/ })

}]);