const lang = Object.freeze(JSON.parse("{\"displayName\":\"Smalltalk\",\"fileTypes\":[\"st\"],\"foldingStartMarker\":\"\\\\[\",\"foldingStopMarker\":\"^(?:\\\\s*|\\\\s)]\",\"name\":\"smalltalk\",\"patterns\":[{\"match\":\"\\\\^\",\"name\":\"keyword.control.flow.return.smalltalk\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.method.begin.smalltalk\"},\"2\":{\"name\":\"entity.name.type.class.smalltalk\"},\"3\":{\"name\":\"keyword.declaration.method.smalltalk\"},\"4\":{\"name\":\"string.quoted.single.protocol.smalltalk\"},\"5\":{\"name\":\"string.quoted.single.protocol.smalltalk\"},\"6\":{\"name\":\"keyword.declaration.method.stamp.smalltalk\"},\"7\":{\"name\":\"string.quoted.single.stamp.smalltalk\"},\"8\":{\"name\":\"string.quoted.single.stamp.smalltalk\"},\"9\":{\"name\":\"punctuation.definition.method.end.smalltalk\"}},\"match\":\"^(!)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\\\\s+(methodsFor:)\\\\s*('([^']*)')(?:\\\\s+(stamp:)\\\\s*('([^']*)'))?\\\\s*(!?)$\",\"name\":\"meta.method.definition.header.smalltalk\"},{\"match\":\"^! !$\",\"name\":\"punctuation.definition.method.end.smalltalk\"},{\"match\":\"\\\\$.\",\"name\":\"constant.character.smalltalk\"},{\"match\":\"\\\\b(class)\\\\b\",\"name\":\"storage.type.$1.smalltalk\"},{\"match\":\"\\\\b(extend|super|self)\\\\b\",\"name\":\"storage.modifier.$1.smalltalk\"},{\"match\":\"\\\\b(yourself|new|Smalltalk)\\\\b\",\"name\":\"keyword.control.$1.smalltalk\"},{\"match\":\"/^:\\\\w*\\\\s*\\\\|/\",\"name\":\"constant.other.block.smalltalk\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.begin.smalltalk\"},\"2\":{\"patterns\":[{\"match\":\"\\\\b[A-Z_a-z][0-9A-Z_a-z]*\\\\b\",\"name\":\"variable.other.local.smalltalk\"}]},\"3\":{\"name\":\"punctuation.definition.variable.end.smalltalk\"}},\"match\":\"(\\\\|)(\\\\s*[A-Z_a-z][0-9A-Z_a-z]*(?:\\\\s+[A-Z_a-z][0-9A-Z_a-z]*)*\\\\s*)(\\\\|)\"},{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.begin.smalltalk\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.end.smalltalk\"}},\"name\":\"meta.block.smalltalk\",\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"match\":\":[A-Z_a-z][0-9A-Z_a-z]*\",\"name\":\"variable.parameter.block.smalltalk\"}]},\"2\":{\"name\":\"punctuation.separator.arguments.block.smalltalk\"}},\"match\":\"((?:\\\\s*:[A-Z_a-z][0-9A-Z_a-z]*)+)\\\\s*(\\\\|)\",\"name\":\"meta.block.arguments.smalltalk\"},{\"include\":\"$self\"}]},{\"include\":\"#numeric\"},{\"match\":\";\",\"name\":\"punctuation.separator.cascade.smalltalk\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.terminator.statement.smalltalk\"},{\"match\":\":=\",\"name\":\"keyword.operator.assignment.smalltalk\"},{\"match\":\"<(?![<=])|>(?![<=>])|<=|>=|==??|~=|~~|>>\",\"name\":\"keyword.operator.comparison.smalltalk\"},{\"match\":\"([-*+/\\\\\\\\])\",\"name\":\"keyword.operator.arithmetic.smalltalk\"},{\"match\":\"(?<=[\\\\t ])!+|\\\\bnot\\\\b|&|\\\\band\\\\b|\\\\||\\\\bor\\\\b\",\"name\":\"keyword.operator.logical.smalltalk\"},{\"match\":\"->|[,@]\",\"name\":\"keyword.operator.misc.smalltalk\"},{\"match\":\"(?<!\\\\.)\\\\b(ensure|resume|retry|signal)\\\\b(?![!?])\",\"name\":\"keyword.control.smalltalk\"},{\"match\":\"\\\\b((?:ifCurtailed|ifTrue|ifFalse|whileFalse|whileTrue):)\\\\b\",\"name\":\"keyword.control.conditionals.smalltalk\"},{\"match\":\"\\\\b(to:do:|do:|timesRepeat:|even|collect:|select:|reject:)\\\\b\",\"name\":\"keyword.control.loop.smalltalk\"},{\"match\":\"\\\\b(initialize|show:|cr|printString|space|new:|at:|at:put:|size|value:??|nextPut:)\\\\b\",\"name\":\"support.function.smalltalk\"},{\"begin\":\"^\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\\\\s+(subclass:)\\\\s*('#?([A-Z_a-z][0-9A-Z_a-z]*)')\",\"beginCaptures\":{\"1\":{\"name\":\"entity.other.inherited-class.smalltalk\"},\"2\":{\"name\":\"keyword.declaration.class.smalltalk\"},\"3\":{\"name\":\"entity.name.type.class.smalltalk\"},\"4\":{\"name\":\"entity.name.type.class.smalltalk\"}},\"end\":\"(?=^\\\\s*!)\",\"name\":\"meta.class.definition.smalltalk\",\"patterns\":[{\"match\":\"\\\\b(instanceVariableNames:|classVariableNames:|poolDictionaries:|category:)\\\\b\",\"name\":\"keyword.declaration.class.variables.smalltalk\"},{\"include\":\"#string_single_quoted\"},{\"include\":\"#comment_block\"}]},{\"begin\":\"\\\"\",\"beginCaptures\":[{\"name\":\"punctuation.definition.comment.begin.smalltalk\"}],\"end\":\"\\\"\",\"endCaptures\":[{\"name\":\"punctuation.definition.comment.end.smalltalk\"}],\"name\":\"comment.block.smalltalk\"},{\"match\":\"\\\\b(true|false)\\\\b\",\"name\":\"constant.language.boolean.smalltalk\"},{\"match\":\"\\\\b(nil)\\\\b\",\"name\":\"constant.language.nil.smalltalk\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.smalltalk\"}},\"match\":\"(#)[A-Z_a-z][0-:A-Z_a-z]*\",\"name\":\"constant.other.symbol.smalltalk\"},{\"begin\":\"#\\\\[\",\"beginCaptures\":[{\"name\":\"punctuation.definition.constant.begin.smalltalk\"}],\"end\":\"]\",\"endCaptures\":[{\"name\":\"punctuation.definition.constant.end.smalltalk\"}],\"name\":\"meta.array.byte.smalltalk\",\"patterns\":[{\"match\":\"[0-9]+(r[0-9A-Za-z]+)?\",\"name\":\"constant.numeric.integer.smalltalk\"},{\"match\":\"[^]\\\\s]+\",\"name\":\"invalid.illegal.character-not-allowed-here.smalltalk\"}]},{\"begin\":\"#\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.constant.array.begin.smalltalk\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.constant.array.end.smalltalk\"}},\"name\":\"constant.other.array.literal.smalltalk\",\"patterns\":[{\"include\":\"#numeric\"},{\"include\":\"#string_single_quoted\"},{\"include\":\"#symbol\"},{\"include\":\"#comment_block\"},{\"include\":\"$self\"}]},{\"begin\":\"'\",\"beginCaptures\":[{\"name\":\"punctuation.definition.string.begin.smalltalk\"}],\"end\":\"'\",\"endCaptures\":[{\"name\":\"punctuation.definition.string.end.smalltalk\"}],\"name\":\"string.quoted.single.smalltalk\"},{\"match\":\"\\\\b[A-Z]\\\\w*\\\\b\",\"name\":\"entity.name.type.class.smalltalk\"}],\"repository\":{\"comment_block\":{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.smalltalk\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.smalltalk\"}},\"name\":\"comment.block.smalltalk\"},\"numeric\":{\"patterns\":[{\"match\":\"(?<!\\\\w)[0-9]+\\\\.[0-9]+s[0-9]*\",\"name\":\"constant.numeric.float.scaled.smalltalk\"},{\"match\":\"(?<!\\\\w)[0-9]+\\\\.[0-9]+([deq]-?[0-9]+)?\",\"name\":\"constant.numeric.float.smalltalk\"},{\"match\":\"(?<!\\\\w)-?[0-9]+r[0-9A-Za-z]+\",\"name\":\"constant.numeric.integer.radix.smalltalk\"},{\"match\":\"(?<!\\\\w)-?[0-9]+([deq]-?[0-9]+)?\",\"name\":\"constant.numeric.integer.smalltalk\"}]},\"string_single_quoted\":{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.smalltalk\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.smalltalk\"}},\"name\":\"string.quoted.single.smalltalk\"},\"symbol\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.symbol.smalltalk\"}},\"match\":\"(#)[A-Z_a-z][0-:A-Z_a-z]*\",\"name\":\"constant.other.symbol.smalltalk\"}},\"scopeName\":\"source.smalltalk\"}"))

export default [
lang
]
