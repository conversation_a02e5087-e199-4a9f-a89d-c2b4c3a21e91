"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-mdx";
exports.ids = ["vendor-chunks/fumadocs-mdx"];
exports.modules = {

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* binding */ _runtime),\n/* harmony export */   createMDXSource: () => (/* binding */ createMDXSource),\n/* harmony export */   resolveFiles: () => (/* binding */ resolveFiles)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n// src/runtime/index.ts\n\nvar cache = /* @__PURE__ */ new Map();\nvar _runtime = {\n  doc(files) {\n    return files.map((file) => {\n      const { default: body, frontmatter, ...exports } = file.data;\n      return {\n        body,\n        ...exports,\n        ...frontmatter,\n        _file: file.info,\n        _exports: file.data,\n        get content() {\n          const path = this._file.absolutePath;\n          const cached = cache.get(path);\n          if (cached) return cached;\n          const content = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(path).toString();\n          cache.set(path, content);\n          return content;\n        }\n      };\n    });\n  },\n  meta(files) {\n    return files.map((file) => {\n      return {\n        ...file.data,\n        _file: file.info\n      };\n    });\n  },\n  docs(docs, metas) {\n    const parsedDocs = this.doc(docs);\n    const parsedMetas = this.meta(metas);\n    return {\n      docs: parsedDocs,\n      meta: parsedMetas,\n      toFumadocsSource() {\n        return createMDXSource(parsedDocs, parsedMetas);\n      }\n    };\n  }\n};\nfunction createMDXSource(docs, meta = []) {\n  return {\n    files: () => resolveFiles({\n      docs,\n      meta\n    })\n  };\n}\nfunction resolveFiles({ docs, meta }) {\n  const outputs = [];\n  for (const entry of docs) {\n    outputs.push({\n      type: \"page\",\n      absolutePath: entry._file.absolutePath,\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  for (const entry of meta) {\n    outputs.push({\n      type: \"meta\",\n      absolutePath: entry._file.absolutePath,\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  return outputs;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* reexport safe */ _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__._runtime),\n/* harmony export */   createMDXSource: () => (/* reexport safe */ _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__.createMDXSource),\n/* harmony export */   resolveFiles: () => (/* reexport safe */ _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__.resolveFiles)\n/* harmony export */ });\n/* harmony import */ var _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-NUDEC6C5.js */ \"(rsc)/./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtbWR4L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUk2QjtBQUszQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Z1bWFkb2NzLXN0YXRpYy1zZWFyY2gvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtbWR4L2Rpc3QvaW5kZXguanM/NTUxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfcnVudGltZSxcbiAgY3JlYXRlTURYU291cmNlLFxuICByZXNvbHZlRmlsZXNcbn0gZnJvbSBcIi4vY2h1bmstTlVERUM2QzUuanNcIjtcbmV4cG9ydCB7XG4gIF9ydW50aW1lLFxuICBjcmVhdGVNRFhTb3VyY2UsXG4gIHJlc29sdmVGaWxlc1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/index.js\n");

/***/ })

};
;