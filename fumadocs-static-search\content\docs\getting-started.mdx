---
title: 快速开始
description: 如何运行与导出本项目
---

## 开发

```bash
pnpm i
pnpm dev
```

访问 [http://localhost:3000/docs](http://localhost:3000/docs)

## 构建 & 静态导出

本项目在 `next.config.mjs` 中启用了 `output: 'export'`，构建时会输出静态站点：

```bash
pnpm build
# 产物位于 out/ 目录，可以丢到任意静态服务器（Nginx、Netlify、Vercel static 等）
```

## 搜索

- 使用 Fumadocs 内置 Orama 搜索
- 通过 `app/api/search/route.ts` 的 `staticGET` 在构建时导出索引
- 前端使用 `type: 'static'` 的搜索客户端在浏览器内完成搜索
