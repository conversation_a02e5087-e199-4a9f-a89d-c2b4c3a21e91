"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-ui_dist_components_dialog_search-default_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-ui/dist/components/dialog/search-default.js":
/*!***************************************************************************!*\
  !*** ./node_modules/fumadocs-ui/dist/components/dialog/search-default.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DefaultSearchDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var fumadocs_core_search_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fumadocs-core/search/client */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/search/client.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var fumadocs_core_utils_use_on_change__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fumadocs-core/utils/use-on-change */ \"(app-pages-browser)/./node_modules/fumadocs-core/dist/utils/use-on-change.js\");\n/* harmony import */ var _contexts_i18n_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/i18n.js */ \"(app-pages-browser)/./node_modules/fumadocs-ui/dist/contexts/i18n.js\");\n/* harmony import */ var _search_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./search.js */ \"(app-pages-browser)/./node_modules/fumadocs-ui/dist/components/dialog/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DefaultSearchDialog(param) {\n    let { defaultTag, tags = [], api, delayMs, type = \"fetch\", allowClear = false, links = [], footer, ...props } = param;\n    _s();\n    const { locale } = (0,_contexts_i18n_js__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const [tag, setTag] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(defaultTag);\n    const { search, setSearch, query } = (0,fumadocs_core_search_client__WEBPACK_IMPORTED_MODULE_1__.useDocsSearch)(type === \"fetch\" ? {\n        type: \"fetch\",\n        api,\n        locale,\n        tag,\n        delayMs\n    } : {\n        type: \"static\",\n        from: api,\n        locale,\n        tag,\n        delayMs\n    });\n    const defaultItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (links.length === 0) return null;\n        return links.map((param)=>{\n            let [name, link] = param;\n            return {\n                type: \"page\",\n                id: name,\n                content: name,\n                url: link\n            };\n        });\n    }, [\n        links\n    ]);\n    (0,fumadocs_core_utils_use_on_change__WEBPACK_IMPORTED_MODULE_3__.useOnChange)(defaultTag, (v)=>{\n        setTag(v);\n    });\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialog, {\n        search: search,\n        onSearchChange: setSearch,\n        isLoading: query.isLoading,\n        ...props,\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogOverlay, {}),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogContent, {\n                children: [\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogHeader, {\n                        children: [\n                            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogIcon, {}),\n                            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogInput, {}),\n                            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogClose, {})\n                        ]\n                    }),\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogList, {\n                        items: query.data !== \"empty\" ? query.data : defaultItems\n                    })\n                ]\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_search_js__WEBPACK_IMPORTED_MODULE_5__.SearchDialogFooter, {\n                children: [\n                    tags.length > 0 && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.TagsList, {\n                        tag: tag,\n                        onTagChange: setTag,\n                        allowClear: allowClear,\n                        children: tags.map((tag)=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_search_js__WEBPACK_IMPORTED_MODULE_5__.TagsListItem, {\n                                value: tag.value,\n                                children: tag.name\n                            }, tag.value))\n                    }),\n                    footer\n                ]\n            })\n        ]\n    });\n}\n_s(DefaultSearchDialog, \"b3te1u0UqSE8YhdjUvoz7bHXktg=\", false, function() {\n    return [\n        _contexts_i18n_js__WEBPACK_IMPORTED_MODULE_4__.useI18n,\n        fumadocs_core_search_client__WEBPACK_IMPORTED_MODULE_1__.useDocsSearch,\n        fumadocs_core_utils_use_on_change__WEBPACK_IMPORTED_MODULE_3__.useOnChange\n    ];\n});\n_c = DefaultSearchDialog;\nvar _c;\n$RefreshReg$(_c, \"DefaultSearchDialog\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-ui/dist/components/dialog/search-default.js\n"));

/***/ })

}]);